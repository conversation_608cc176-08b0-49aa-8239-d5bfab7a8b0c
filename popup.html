<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Extension</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="container">
    <header class="header">
      <div class="logo-container">
        <img src="../../icons/icon128.png" alt="Extension" class="logo">
      </div>
      <div class="brand-text">
        <h1 class="brand-title">Loading...</h1>
        <p class="brand-subtitle">Please wait...</p>
      </div>
    </header>

    <section class="card api-section">
      <h2 class="section-title">
        <div class="section-icon">🔑</div>
        <span id="api-key-label">OpenRouter Configuration</span>
        <div class="status-badge disconnected" id="connection-status-badge">Not Connected</div>
      </h2>

      <div class="select-group">
        <label class="input-label">LLM Model</label>
        <div class="select-wrapper">
          <select id="model-select" class="select">
            <option value="anthropic/claude-sonnet-4">Claude Sonnet 4 (🚀 Awesome)</option>
            <option value="moonshotai/kimi-k2">Kimi K2 (⚡ Super good)</option>
            <option value="openai/gpt-4.1">OpenAI GPT-4.1 (🔥 Pretty good)</option>
            <option value="deepseek/deepseek-chat-v3-0324">DeepSeek Chat V3 (🔥 Pretty good)</option>
            <option value="google/gemini-2.5-pro-preview">Gemini 2.5 Pro (🐌 Good but slow)</option>
            <option value="x-ai/grok-4">Grok 4 (🐌 Good but slow)</option>
            <option value="mistralai/mistral-large-2407">Mistral Large (⚡ Decent but fast)</option>
          </select>
          <div class="select-icon">
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="6,9 12,15 18,9"></polyline>
            </svg>
          </div>
        </div>
      </div>

      <div class="input-group">
        <div class="input-wrapper">
          <label class="floating-label" for="api-key">Enter your OpenRouter API key</label>
          <input type="password" id="api-key" class="input" placeholder="sk-or-v1-...">
          <div class="input-icon" id="toggle-visibility">
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
              <circle cx="12" cy="12" r="3"></circle>
            </svg>
          </div>
        </div>
      </div>

      <div class="button-group">
        <button class="btn btn-primary" id="save-key" style="position: relative; overflow: hidden;">
          <div class="btn-spinner"></div>
          <span class="btn-text">Save Configuration</span>
        </button>
      </div>
    </section>

    <section class="card theme-section">
      <h2 class="section-title">
        <div class="section-icon">🎨</div>
        UI Theme
      </h2>

      <div class="theme-options">
        <div class="theme-option" data-theme="matrix">
          <input type="radio" id="ui-theme-matrix" name="fabChatTheme" value="matrix" checked>
          <label for="ui-theme-matrix" class="theme-label">
            <div class="theme-preview matrix-ui-preview"></div>
            <div class="theme-info">
              <div class="theme-name">⚡ Matrix Green</div>
              <div class="theme-description">Classic green cyberpunk interface</div>
            </div>
          </label>
        </div>

        <div class="theme-option" data-theme="light">
          <input type="radio" id="ui-theme-light" name="fabChatTheme" value="light">
          <label for="ui-theme-light" class="theme-label">
            <div class="theme-preview light-ui-preview"></div>
            <div class="theme-info">
              <div class="theme-name">☀️ Light Mode</div>
              <div class="theme-description">Clean white background interface</div>
            </div>
          </label>
        </div>

        <div class="theme-option" data-theme="dark">
          <input type="radio" id="ui-theme-dark" name="fabChatTheme" value="dark">
          <label for="ui-theme-dark" class="theme-label">
            <div class="theme-preview dark-ui-preview"></div>
            <div class="theme-info">
              <div class="theme-name">🌙 Dark Mode</div>
              <div class="theme-description">Clean black background interface</div>
            </div>
          </label>
        </div>
      </div>
    </section>

    <section class="card theme-section">
      <h2 class="section-title">
        <div class="section-icon">🌌</div>
        Connection Theme
      </h2>

      <div class="theme-options">
        <div class="theme-option" data-theme="matrix">
          <input type="radio" id="theme-matrix" name="connectionTheme" value="matrix" checked>
          <label for="theme-matrix" class="theme-label">
            <div class="theme-preview matrix-preview"></div>
            <div class="theme-info">
              <div class="theme-name">Green Matrix</div>
              <div class="theme-description">Classic green digital stream</div>
            </div>
          </label>
        </div>

        <div class="theme-option" data-theme="quantum">
          <input type="radio" id="theme-quantum" name="connectionTheme" value="quantum">
          <label for="theme-quantum" class="theme-label">
            <div class="theme-preview quantum-preview"></div>
            <div class="theme-info">
              <div class="theme-name">Galaxy Quantum</div>
              <div class="theme-description">Color-shifting particle stream</div>
            </div>
          </label>
        </div>

        <div class="theme-option" data-theme="dottrail">
          <input type="radio" id="theme-dottrail" name="connectionTheme" value="dottrail">
          <label for="theme-dottrail" class="theme-label">
            <div class="theme-preview dottrail-preview"></div>
            <div class="theme-info">
              <div class="theme-name">Purple Dot Trail</div>
              <div class="theme-description">Lightweight moving dots</div>
            </div>
          </label>
        </div>

        <div class="theme-option" data-theme="disabled">
          <input type="radio" id="theme-disabled" name="connectionTheme" value="disabled">
          <label for="theme-disabled" class="theme-label">
            <div class="theme-preview disabled-preview"></div>
            <div class="theme-info">
              <div class="theme-name">🚫 Disabled</div>
              <div class="theme-description">No visual effects or animations</div>
            </div>
          </label>
        </div>
      </div>
    </section>

    <footer class="footer">
      Created by <a href="https://kenkais.com" target="_blank">Loading...</a> |
      <a href="https://www.skool.com/kenkai/about" target="_blank">AI Community</a>
    </footer>
  </div>

  <script src="llm-config-popup.js"></script>
  <script src="popup.js"></script>
</body>
</html>
