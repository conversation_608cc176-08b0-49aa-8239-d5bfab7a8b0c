{"simple-agent": {"id": "simple-agent", "name": "Simple Agent", "description": "AI Agent with chat trigger, OpenAI model, and memory buffer for conversational workflows", "icon": "🤖", "tags": ["ai", "agent", "chat", "openai", "memory"], "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-800, -1000], "id": "5544501e-e91d-4421-90a7-818496e1c2c1", "name": "When chat message received", "webhookId": "96660817-2ec0-452a-a68e-1004b6433d77"}, {"parameters": {"options": {"systemMessage": "Create an prompt for an AI Agent that.."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [-520, -1000], "id": "8c73a77b-3674-4d64-8c60-203e7ca3934e", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-500, -780], "id": "2806f292-f9e9-4dbd-a253-a03f2ac3eedf", "name": "4.1-mini", "credentials": {"openAiApi": {"id": "w4w8hkdwHFX6SE1X", "name": "Tasks"}}}, {"parameters": {"contextWindowLength": 10}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-380, -780], "id": "0e051afb-1b9a-4e9d-b71a-34b1c29e5fe8", "name": "Memory"}], "connections": {"When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "4.1-mini": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}}}, "http-loop": {"id": "http-loop", "name": "HTTP + Loop", "description": "HTTP request with split out and loop over items for batch processing workflows", "icon": "🔄", "tags": ["http", "loop", "batch", "split", "processing"], "nodes": [{"parameters": {"url": "Place your endpoint here", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [560, -880], "id": "e5047669-9715-4237-9a62-9b6719b43357", "name": "HTTP Request"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [280, -880], "id": "21829d8b-77e1-4e6f-8e6f-03d772cab9ab", "name": "Loop Over Items"}, {"parameters": {"fieldToSplitOut": "Put the field you want to split out here", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-60, -880], "id": "bcce41ca-8876-4177-9003-0887a132fb33", "name": "Split Out"}, {"parameters": {"jsCode": "Extract all items from these inputs using no indexes:\n\n1. Input item 1\n2. Input item 2\n3. Input item 3\n4. Input item 4\n5. Input item 5\n..etc"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [560, -1040], "id": "a80f4d88-2c5b-4f16-8102-e77abe0390a5", "name": "Extractx"}], "connections": {"HTTP Request": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Extractx", "type": "main", "index": 0}], [{"node": "HTTP Request", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}}}, "http-extract": {"id": "http-extract", "name": "HTTP + Extract", "description": "Simple HTTP request with code extraction for data processing workflows", "icon": "🔗", "tags": ["http", "extract", "code", "processing"], "nodes": [{"parameters": {"url": "Place your endpoint here", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1320, -1020], "id": "a8d6165e-00a7-4223-bcbf-772b1bec49d3", "name": "HTTP Requestx"}, {"parameters": {"jsCode": "Extract all items from these inputs using no indexes:\n\n1. Input item 1\n2. Input item 2\n3. Input item 3\n4. Input item 4\n5. Input item 5\n..etc"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1580, -1020], "id": "e27d98dc-ad9f-49fa-80da-81ae6c183f3b", "name": "Extract"}], "connections": {"HTTP Requestx": {"main": [[{"node": "Extract", "type": "main", "index": 0}]]}, "Extract": {"main": [[]]}}}, "text-classifier": {"id": "text-classifier", "name": "Text Classifier", "description": "Text classification using AI with OpenAI model for content categorization", "icon": "📊", "tags": ["ai", "classification", "text", "openai", "categorization"], "nodes": [{"parameters": {"inputText": "Place the input text here", "categories": {"categories": [{"category": "Category 1", "description": "What should pass here?"}, {"category": "Category 2", "description": "What should pass here?"}, {"category": "Category 3", "description": "What should pass here?"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.textClassifier", "typeVersion": 1, "position": [2140, -1000], "id": "2f336068-5596-402d-bcd0-664db1f0d686", "name": "Text Classifier"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [2160, -820], "id": "f90b88d9-8228-41e4-939c-c058caa7539c", "name": "4.1-minix", "credentials": {"openAiApi": {"id": "w4w8hkdwHFX6SE1X", "name": "Tasks"}}}], "connections": {"4.1-minix": {"ai_languageModel": [[{"node": "Text Classifier", "type": "ai_languageModel", "index": 0}]]}}}, "blotato-poster": {"id": "blotato-poster", "name": "<PERSON><PERSON><PERSON>", "description": "Multi-platform social media posting workflow using Blotato API for Instagram, YouTube, TikTok, Facebook, Twitter, LinkedIn, Threads, BlueSky, and Pinterest", "icon": "📱", "tags": ["social", "media", "posting", "blotato", "api", "multi-platform"], "nodes": [{"parameters": {"method": "POST", "url": "https://backend.blotato.com/v2/media", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "blotato-api-key", "value": "YOUR_API_KEY"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "url", "value": "=<final video expression url>"}]}, "options": {}}, "id": "12227ae5-c85e-43b3-8f71-4ca0d1ebfdb6", "name": "Upload Video to Blotato", "type": "n8n-nodes-base.httpRequest", "position": [1860, 760], "typeVersion": 4.2}, {"parameters": {"method": "POST", "url": "https://backend.blotato.com/v2/posts", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "blotato-api-key", "value": "=YOUR_API_KEY"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Your IDs').item.json.instagram_id }}\",\n    \"target\": {\n      \"targetType\": \"instagram\"\n    },\n    \"content\": {\n      \"text\": \"{{ $('Save Rewritten Video to Google Sheets').item.json.Caption }}\",\n      \"platform\": \"instagram\",\n      \"mediaUrls\": [\n        \"{{ $json.url }}\"\n      ]\n    }\n  }\n}\n\n", "options": {}}, "id": "5dfdeaf6-a1a9-4419-a38a-b6645e7338a2", "name": "INSTAGRAM", "type": "n8n-nodes-base.httpRequest", "position": [2140, 520], "typeVersion": 4.2}, {"parameters": {"method": "POST", "url": "https://backend.blotato.com/v2/posts", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "blotato-api-key", "value": "YOUR_API_KEY"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Your IDs').item.json.youtube_id }}\",\n    \"target\": {\n      \"targetType\": \"youtube\",\n      \"title\": \"{{ $('Save Rewritten Video to Google Sheets').item.json['Texte superposé'] }}\",\n      \"privacyStatus\": \"unlisted\",\n      \"shouldNotifySubscribers\": \"false\"\n    },\n    \"content\": {\n      \"text\": \"{{ $('Save Rewritten Video to Google Sheets').item.json.Caption }}\",\n      \"platform\": \"youtube\",\n      \"mediaUrls\": [\n        \"{{ $json.url }}\"\n      ]\n    }\n  }\n}\n", "options": {}}, "id": "ad6e17d2-8a80-412e-aa3b-f4fdc96bd5ee", "name": "YOUTUBE", "type": "n8n-nodes-base.httpRequest", "position": [2360, 520], "typeVersion": 4.2}, {"parameters": {"method": "POST", "url": "https://backend.blotato.com/v2/posts", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "blotato-api-key", "value": "YOUR_API_KEY"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Your IDs').item.json.tiktok_id }}\",\n    \"target\": {\n      \"targetType\": \"tiktok\",\n      \"isYourBrand\": \"false\", \n      \"disabledDuet\": \"false\",\n      \"privacyLevel\": \"PUBLIC_TO_EVERYONE\",\n      \"isAiGenerated\": \"true\",\n      \"disabledStitch\": \"false\",\n      \"disabledComments\": \"false\",\n      \"isBrandedContent\": \"false\"\n      \n    },\n    \"content\": {\n      \"text\": \"{{ $('Save Rewritten Video to Google Sheets').item.json.Caption }}\",\n      \"platform\": \"tiktok\",\n      \"mediaUrls\": [\n        \"{{ $json.url }}\"\n      ]\n    }\n  }\n}\n", "options": {}}, "id": "d8b291cd-0bf8-49dd-942c-7b6759f502c2", "name": "TIKTOK", "type": "n8n-nodes-base.httpRequest", "position": [2560, 520], "typeVersion": 4.2}, {"parameters": {"method": "POST", "url": "https://backend.blotato.com/v2/posts", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "blotato-api-key", "value": "YOUR_API_KEY"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Your IDs').item.json.facebook_id }}\",\n    \"target\": {\n      \"targetType\": \"facebook\",\n      \"pageId\": \"{{ $('Your IDs').item.json.facebook_page_id }}\"\n\n      \n    },\n    \"content\": {\n      \"text\": \"{{ $('Save Rewritten Video to Google Sheets').item.json.Caption }}\",\n      \"platform\": \"facebook\",\n      \"mediaUrls\": [\n        \"{{ $json.url }}\"\n      ]\n    }\n  }\n}", "options": {}}, "id": "7a958a06-da46-4f8e-a75a-6460a601b692", "name": "FACEBOOK", "type": "n8n-nodes-base.httpRequest", "position": [2140, 760], "typeVersion": 4.2}, {"parameters": {"method": "POST", "url": "https://backend.blotato.com/v2/posts", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "blotato-api-key", "value": "YOUR_API_KEY"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Your IDs').item.json.threads_id }}\",\n    \"target\": {\n      \"targetType\": \"threads\"\n      \n    },\n    \"content\": {\n      \"text\": \"{{ $('Save Rewritten Video to Google Sheets').item.json.Caption }}\",\n      \"platform\": \"threads\",\n      \"mediaUrls\": [\n        \"{{ $json.url }}\"\n      ]\n    }\n  }\n}\n", "options": {}}, "id": "430e7bf4-15b1-4e7a-b30c-de7e51edd060", "name": "THREADS", "type": "n8n-nodes-base.httpRequest", "position": [2360, 760], "typeVersion": 4.2}, {"parameters": {"method": "POST", "url": "https://backend.blotato.com/v2/posts", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "blotato-api-key", "value": "=YOUR_API_KEY"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Your IDs').item.json.twitter_id }}\",\n    \"target\": {\n      \"targetType\": \"twitter\"\n      \n    },\n    \"content\": {\n      \"text\": \"{{ $('Save Rewritten Video to Google Sheets').item.json.Caption }}\",\n      \"platform\": \"twitter\",\n      \"mediaUrls\": [\n        \"{{ $json.url }}\"\n      ]\n    }\n  }\n}\n", "options": {}}, "id": "0e4a9bb0-4b60-4fa8-92c3-ca44657d4e1f", "name": "X", "type": "n8n-nodes-base.httpRequest", "position": [2560, 760], "typeVersion": 4.2}, {"parameters": {"method": "POST", "url": "https://backend.blotato.com/v2/posts", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "blotato-api-key", "value": "YOUR_API_KEY"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Your IDs').item.json.linkedin_id }}\",\n    \"target\": {\n      \"targetType\": \"linkedin\"\n      \n    },\n    \"content\": {\n      \"text\": \"{{ $('Save Rewritten Video to Google Sheets').item.json.Caption }}\",\n      \"platform\": \"linkedin\",\n      \"mediaUrls\": [\n        \"{{ $json.url }}\"\n      ]\n    }\n  }\n}\n", "options": {}}, "id": "52e646d3-95e8-44f6-a4c3-a667de5cded6", "name": "LINKEDIN", "type": "n8n-nodes-base.httpRequest", "position": [2140, 1000], "typeVersion": 4.2}, {"parameters": {"method": "POST", "url": "https://backend.blotato.com/v2/posts", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "blotato-api-key", "value": "YOUR_API_KEY"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "= {\n  \"post\": {\n    \"accountId\": \"{{ $('Your IDs').item.json.bluesky_id }}\",\n    \"target\": {\n      \"targetType\": \"bluesky\"\n      \n    },\n    \"content\": {\n      \"text\": \"{{ $('Save Rewritten Video to Google Sheets').item.json.Caption }}\",\n      \"platform\": \"bluesky\",\n      \"mediaUrls\": [\n        \"https://pbs.twimg.com/media/GE8MgIiWEAAfsK3.jpg\"\n      ]\n    }\n  }\n}\n", "options": {}}, "id": "9699d945-d6fa-4032-b3b8-df2fa46debcf", "name": "BLUESKY", "type": "n8n-nodes-base.httpRequest", "position": [2360, 1000], "typeVersion": 4.2}, {"parameters": {"method": "POST", "url": "https://backend.blotato.com/v2/posts", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "blotato-api-key", "value": "YOUR_API_KEY"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"post\": {\n    \"accountId\": \"{{ $('Your IDs').item.json.pinterest_id }}\",\n    \"target\": {\n      \"targetType\": \"pinterest\",\n      \"boardId\": \"{{ $('Your IDs').item.json.pinterest_board_id }}\"      \n    },\n    \"content\": {\n      \"text\": \"{{ $('Save Rewritten Video to Google Sheets').item.json.Caption }}\",\n      \"platform\": \"pinterest\",\n      \"mediaUrls\": [\n        \"https://pbs.twimg.com/media/GE8MgIiWEAAfsK3.jpg\"\n      ]\n    }\n  }\n}\n\n", "options": {}}, "id": "7f9a1a7d-efef-48f9-bb59-2b311bbef3e2", "name": "PINTEREST", "type": "n8n-nodes-base.httpRequest", "position": [2560, 1000], "typeVersion": 4.2}, {"parameters": {"mode": "raw", "jsonOutput": "{\n  \"instagram_id\": \"0000\",\n  \"youtube_id\": \"0000\",\n  \"threads_id\": \"0000\",\n  \"tiktok_id\": \"0000\",\n  \"facebook_id\": \"0000\",\n  \"facebook_page_id\": \"**********00\",\n  \"twitter_id\": \"0000\",\n  \"linkedin_id\": \"0000\",\n  \"pinterest_id\": \"0000\",\n  \"pinterest_board_id\": \"********************\",\n  \"bluesky_id\": \"0000\"\n}\n", "options": {}}, "id": "88c27eaa-49b6-4f1e-8091-254555c23c89", "name": "Your IDs", "type": "n8n-nodes-base.set", "position": [1640, 760], "typeVersion": 3.4}], "connections": {"Upload Video to Blotato": {"main": [[{"node": "INSTAGRAM", "type": "main", "index": 0}, {"node": "YOUTUBE", "type": "main", "index": 0}, {"node": "TIKTOK", "type": "main", "index": 0}, {"node": "FACEBOOK", "type": "main", "index": 0}, {"node": "THREADS", "type": "main", "index": 0}, {"node": "X", "type": "main", "index": 0}, {"node": "LINKEDIN", "type": "main", "index": 0}, {"node": "BLUESKY", "type": "main", "index": 0}, {"node": "PINTEREST", "type": "main", "index": 0}]]}, "Your IDs": {"main": [[{"node": "Upload Video to Blotato", "type": "main", "index": 0}]]}}}, "telegram-ai-bot": {"id": "telegram-ai-bot", "name": "Telegram AI Bot", "description": "Complete Telegram bot with AI agent, memory, and chat ID filtering for personalized conversations", "icon": "🤖", "tags": ["telegram", "ai", "bot", "chat", "openai", "memory", "messaging"], "nodes": [{"parameters": {"additionalFields": {}, "updates": ["*"]}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [100, 100], "id": "89511335-f65e-431b-a0a8-0bce3a39f247", "name": "<PERSON>eg<PERSON>", "credentials": {"telegramApi": {"id": "KJyzlMiTiM9nlJfK", "name": "Nadia"}}, "webhookId": "9191d30b-be2d-4d7b-beb9-d60b6cef8dbf"}, {"parameters": {"options": {"systemMessage": "Create a prompt for a.."}, "promptType": "define", "text": "={{ $json.message }}"}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [760, 100], "id": "09c9e7aa-2f24-47f7-8a1b-26428cd0d4f1", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "cachedResultName": "gpt-4.1-mini", "mode": "list", "value": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [788, 320], "id": "901fa2fd-a9f4-4fa1-a912-99e26b0a3fe0", "name": "4.1-mini", "credentials": {"openAiApi": {"id": "w4w8hkdwHFX6SE1X", "name": "Tasks"}}}, {"parameters": {"contextWindowLength": 10, "sessionIdType": "customKey", "sessionKey": "memory"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [908, 320], "id": "d7bf8720-0809-46cc-a552-9d625ba2eb63", "name": "Memory"}, {"parameters": {"conditions": {"combinator": "and", "conditions": [{"id": "1722a9a4-860b-412e-93a6-cdfd665e6cf1", "leftValue": "={{ $json.message.from.id }}", "operator": {"operation": "equals", "type": "number"}, "rightValue": {}}], "options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [320, 100], "id": "ad0e43cd-f335-4e27-8e15-9c092bab1714", "name": "Your Chat ID Here"}, {"parameters": {"assignments": {"assignments": [{"id": "e99cc4a9-4c78-48a5-b129-6a7a4c4bdc34", "name": "message", "type": "string", "value": "={{ $json.message.text }}"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [540, 100], "id": "193d1b28-d3f9-4417-abc7-2db26bc9c183", "name": "Set Message"}, {"parameters": {"additionalFields": {"appendAttribution": false}, "chatId": "={{ $('Telegram Trigger').item.json.message.from.id }}", "text": "={{ $json.output }}"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1136, 100], "id": "4fda1191-cdbd-496f-beb7-b3a176586e3f", "name": "Reply", "credentials": {"telegramApi": {"id": "KJyzlMiTiM9nlJfK", "name": "Nadia"}}, "webhookId": "b83eddc6-8ff0-4a81-a8bc-0ac695c5f4d0"}], "connections": {"4.1-mini": {"ai_languageModel": [[{"index": 0, "node": "AI Agent", "type": "ai_languageModel"}]]}, "AI Agent": {"main": [[{"index": 0, "node": "Reply", "type": "main"}]]}, "Memory": {"ai_memory": [[{"index": 0, "node": "AI Agent", "type": "ai_memory"}]]}, "Set Message": {"main": [[{"index": 0, "node": "AI Agent", "type": "main"}]]}, "Telegram Trigger": {"main": [[{"index": 0, "node": "Your Chat ID Here", "type": "main"}]]}, "Your Chat ID Here": {"main": [[{"index": 0, "node": "Set Message", "type": "main"}]]}}}}