<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>n8n Boy Extension Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .model-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin-top: 10px;
        }
        .model-item {
            padding: 5px;
            margin: 2px 0;
            background-color: #f8f9fa;
            border-radius: 3px;
        }
        .gemini-model {
            background-color: #e3f2fd;
            border-left: 3px solid #2196f3;
        }
        .openrouter-model {
            background-color: #f3e5f5;
            border-left: 3px solid #9c27b0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>n8n Boy Extension - Gemini API Integration Test</h1>
        
        <div class="test-section">
            <h3>Configuration Loading Test</h3>
            <div id="config-status" class="status">Testing...</div>
            <div id="config-details"></div>
        </div>
        
        <div class="test-section">
            <h3>Available Models</h3>
            <div id="models-status" class="status">Loading...</div>
            <div id="models-list" class="model-list"></div>
        </div>
        
        <div class="test-section">
            <h3>API Provider Detection Test</h3>
            <div id="provider-status" class="status">Testing...</div>
            <div id="provider-details"></div>
        </div>
        
        <div class="test-section">
            <h3>API Key Validation Test</h3>
            <div id="validation-status" class="status">Testing...</div>
            <div id="validation-details"></div>
        </div>
    </div>

    <!-- Load the extension files -->
    <script src="llm-config-global.js"></script>
    <script>
        // Test configuration loading
        function testConfiguration() {
            const configStatus = document.getElementById('config-status');
            const configDetails = document.getElementById('config-details');
            
            try {
                if (typeof window.LLM_CONFIG === 'undefined') {
                    throw new Error('LLM_CONFIG not loaded');
                }
                
                const config = window.LLM_CONFIG;
                
                // Check if dual API configuration exists
                const hasOpenRouter = config.openrouter && config.openrouter.apiEndpoint;
                const hasGemini = config.gemini && config.gemini.apiEndpoint;
                
                if (hasOpenRouter && hasGemini) {
                    configStatus.textContent = 'SUCCESS';
                    configStatus.className = 'status success';
                    configDetails.innerHTML = `
                        <strong>OpenRouter:</strong> ${config.openrouter.apiEndpoint}<br>
                        <strong>Gemini:</strong> ${config.gemini.apiEndpoint}<br>
                        <strong>Default Model:</strong> ${config.defaultModel}
                    `;
                } else {
                    throw new Error('Dual API configuration not found');
                }
            } catch (error) {
                configStatus.textContent = 'ERROR';
                configStatus.className = 'status error';
                configDetails.textContent = error.message;
            }
        }
        
        // Test available models
        function testModels() {
            const modelsStatus = document.getElementById('models-status');
            const modelsList = document.getElementById('models-list');
            
            try {
                const models = window.LLM_CONFIG.models;
                let geminiCount = 0;
                let openrouterCount = 0;
                let modelsHtml = '';
                
                for (const [modelId, modelConfig] of Object.entries(models)) {
                    const isGemini = modelConfig.apiProvider === 'gemini';
                    const cssClass = isGemini ? 'gemini-model' : 'openrouter-model';
                    
                    if (isGemini) {
                        geminiCount++;
                    } else {
                        openrouterCount++;
                    }
                    
                    modelsHtml += `
                        <div class="model-item ${cssClass}">
                            <strong>${modelConfig.name}</strong><br>
                            <small>ID: ${modelId} | Provider: ${modelConfig.apiProvider || 'openrouter'}</small>
                        </div>
                    `;
                }
                
                modelsStatus.textContent = `SUCCESS (${geminiCount} Gemini + ${openrouterCount} OpenRouter)`;
                modelsStatus.className = 'status success';
                modelsList.innerHTML = modelsHtml;
                
            } catch (error) {
                modelsStatus.textContent = 'ERROR';
                modelsStatus.className = 'status error';
                modelsList.textContent = error.message;
            }
        }
        
        // Test provider detection
        function testProviderDetection() {
            const providerStatus = document.getElementById('provider-status');
            const providerDetails = document.getElementById('provider-details');
            
            try {
                // Test cases for provider detection
                const testCases = [
                    { model: 'gemini-2.0-flash-exp', expected: 'gemini' },
                    { model: 'gemini-1.5-pro', expected: 'gemini' },
                    { model: 'anthropic/claude-sonnet-4', expected: 'openrouter' },
                    { model: 'openai/gpt-4.1', expected: 'openrouter' }
                ];
                
                let results = '';
                let allPassed = true;
                
                for (const testCase of testCases) {
                    const modelConfig = window.LLM_CONFIG.models[testCase.model];
                    const actualProvider = modelConfig ? modelConfig.apiProvider : 'unknown';
                    const passed = actualProvider === testCase.expected;
                    
                    if (!passed) allPassed = false;
                    
                    results += `
                        <div style="margin: 5px 0; color: ${passed ? 'green' : 'red'}">
                            ${testCase.model}: ${actualProvider} ${passed ? '✓' : '✗'}
                        </div>
                    `;
                }
                
                if (allPassed) {
                    providerStatus.textContent = 'SUCCESS';
                    providerStatus.className = 'status success';
                } else {
                    providerStatus.textContent = 'PARTIAL';
                    providerStatus.className = 'status error';
                }
                
                providerDetails.innerHTML = results;
                
            } catch (error) {
                providerStatus.textContent = 'ERROR';
                providerStatus.className = 'status error';
                providerDetails.textContent = error.message;
            }
        }
        
        // Test API key validation
        function testApiKeyValidation() {
            const validationStatus = document.getElementById('validation-status');
            const validationDetails = document.getElementById('validation-details');
            
            try {
                // Test API key format validation
                const testKeys = [
                    { key: 'sk-or-v1-1234567890123456789012345678901234567890', provider: 'openrouter', expected: true },
                    { key: 'AIzaSyDXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX', provider: 'gemini', expected: true },
                    { key: 'invalid-key', provider: 'openrouter', expected: false },
                    { key: 'sk-or-v1-short', provider: 'openrouter', expected: false }
                ];
                
                let results = '';
                let allPassed = true;
                
                for (const testKey of testKeys) {
                    // Simple validation logic (since we can't access popup.js validation here)
                    let isValid = false;
                    if (testKey.provider === 'gemini') {
                        isValid = testKey.key.startsWith('AIza') && testKey.key.length >= 35;
                    } else {
                        isValid = testKey.key.startsWith('sk-or-v1-') && testKey.key.length >= 30;
                    }
                    
                    const passed = isValid === testKey.expected;
                    if (!passed) allPassed = false;
                    
                    results += `
                        <div style="margin: 5px 0; color: ${passed ? 'green' : 'red'}">
                            ${testKey.provider}: ${testKey.key.substring(0, 20)}... ${passed ? '✓' : '✗'}
                        </div>
                    `;
                }
                
                if (allPassed) {
                    validationStatus.textContent = 'SUCCESS';
                    validationStatus.className = 'status success';
                } else {
                    validationStatus.textContent = 'FAILED';
                    validationStatus.className = 'status error';
                }
                
                validationDetails.innerHTML = results;
                
            } catch (error) {
                validationStatus.textContent = 'ERROR';
                validationStatus.className = 'status error';
                validationDetails.textContent = error.message;
            }
        }
        
        // Run all tests when page loads
        window.addEventListener('load', function() {
            setTimeout(() => {
                testConfiguration();
                testModels();
                testProviderDetection();
                testApiKeyValidation();
            }, 100);
        });
    </script>
</body>
</html>
