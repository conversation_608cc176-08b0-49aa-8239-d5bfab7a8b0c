# n8n Boy Extension - Setup Guide for Gemini API

## Quick Start

### Step 1: Get Your Gemini API Key
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated API key (starts with `<PERSON><PERSON>`)

### Step 2: Configure the Extension
1. Click the n8n Boy extension icon in your browser toolbar
2. In the popup, you'll see fields for API configuration
3. Paste your Gemini API key in the appropriate field
4. Select a Gemini model from the dropdown (e.g., "Gemini 2.0 Flash (⚡ Direct API)")
5. Click "Save" or the extension will auto-save

### Step 3: Test the Integration
1. Navigate to your n8n workflow
2. Try using any of the extension's AI features:
   - Code generation in Code nodes
   - JSON template generation
   - Multi-agent chat assistance
3. Verify that responses are coming from the Gemini API

## Detailed Configuration

### Understanding API Providers

#### OpenRouter (Existing)
- **Purpose**: Access to multiple AI models through a single API
- **API Key Format**: `sk-or-v1-xxxxxxxxxx`
- **Benefits**: Multiple model options, unified billing
- **Use Case**: When you want access to various AI models

#### Gemini Direct API (New)
- **Purpose**: Direct access to Google's Gemini models
- **API Key Format**: `AIzaxxxxxxxxxx`
- **Benefits**: Better performance, lower latency, direct billing
- **Use Case**: When you primarily use Gemini models

### Model Selection Guide

#### Choose Gemini 2.0 Flash if:
- You want the latest Gemini capabilities
- Speed is important for your workflow
- You're doing general-purpose AI tasks

#### Choose Gemini 1.5 Pro if:
- You need maximum reasoning capability
- Working with complex, multi-step problems
- Quality is more important than speed

#### Choose Gemini 1.5 Flash if:
- You need a balance of speed and capability
- Processing large volumes of requests
- Cost efficiency is important

### Extension Popup Configuration

The extension popup will show different sections based on your setup:

```
┌─────────────────────────────────┐
│         n8n Boy Settings        │
├─────────────────────────────────┤
│ API Provider: [Dropdown]        │
│ ○ OpenRouter                    │
│ ● Gemini Direct                 │
├─────────────────────────────────┤
│ API Key: [Input Field]          │
│ AIzaSyxxxxxxxxxxxxxxxxxx        │
├─────────────────────────────────┤
│ Model: [Dropdown]               │
│ Gemini 2.0 Flash (⚡ Direct)    │
├─────────────────────────────────┤
│ [Save Configuration]            │
└─────────────────────────────────┘
```

## Advanced Configuration

### Multiple API Keys
You can configure both OpenRouter and Gemini API keys simultaneously:

1. **Set OpenRouter Key**: Enter your OpenRouter API key
2. **Set Gemini Key**: Enter your Gemini API key
3. **Switch Between Providers**: Change the selected model to switch providers automatically

### Model-Specific Settings

Each model has optimized settings:

#### Gemini Models
- **Temperature**: Automatically adjusted for different use cases
- **Max Tokens**: Set to 3000 for optimal performance
- **Context Length**: Up to 2M tokens for Gemini 1.5 Pro

#### OpenRouter Models
- **Temperature**: Varies by model and use case
- **Max Tokens**: 3000 default
- **Context Length**: Varies by model

### Browser Storage

The extension stores configuration in Chrome's sync storage:

```javascript
// Storage keys used by the extension
{
  "openrouterApiKey": "sk-or-v1-your-key-here",
  "geminiApiKey": "AIzaSy-your-key-here",
  "selectedModel": "gemini-2.0-flash-exp"
}
```

## Troubleshooting

### Common Setup Issues

#### 1. API Key Not Recognized
**Problem**: Extension shows "Please set your API key"
**Solutions**:
- Verify the API key format is correct
- Check for extra spaces or characters
- Ensure the key hasn't expired
- Try refreshing the extension

#### 2. Model Not Available
**Problem**: Selected model doesn't work
**Solutions**:
- Check if you have access to the model
- Verify your API key permissions
- Try a different model
- Check Google AI Studio for model availability

#### 3. Connection Errors
**Problem**: "Failed to connect to API"
**Solutions**:
- Check your internet connection
- Verify firewall settings
- Try a different network
- Check if the API service is operational

### Debug Mode

Enable debug mode to see detailed information:

1. Open browser developer tools (F12)
2. Go to Console tab
3. Look for n8n Boy debug messages
4. Check for API request/response details

### Testing Your Setup

#### Quick Test
1. Open any n8n workflow
2. Add a Code node
3. Right-click and select "Generate with AI"
4. Enter a simple request like "return current date"
5. Verify the code is generated successfully

#### Advanced Test
1. Use the multi-agent chat feature
2. Ask a complex question about n8n workflows
3. Verify you get a detailed, helpful response
4. Check that the response indicates which model was used

## Best Practices

### API Key Security
- **Never share your API keys** in code or documentation
- **Use environment variables** for production deployments
- **Rotate keys regularly** for security
- **Monitor usage** to detect unauthorized access

### Model Selection Strategy
- **Start with Gemini 2.0 Flash** for general use
- **Upgrade to Gemini 1.5 Pro** for complex tasks
- **Keep OpenRouter as backup** for model diversity
- **Test different models** to find what works best

### Performance Optimization
- **Use appropriate models** for each task type
- **Monitor response times** and adjust accordingly
- **Cache results** when possible
- **Batch similar requests** to reduce API calls

## Migration from OpenRouter

### Gradual Migration
1. **Keep existing OpenRouter setup** working
2. **Add Gemini API key** as secondary option
3. **Test Gemini models** with non-critical tasks
4. **Gradually switch** to Gemini for primary use
5. **Keep OpenRouter** as fallback option

### Performance Comparison
Test both providers with your typical use cases:
- **Response time**: Measure average response times
- **Quality**: Compare output quality for your tasks
- **Reliability**: Monitor error rates and uptime
- **Cost**: Compare pricing for your usage patterns

## Support and Resources

### Getting Help
- Check the browser console for error messages
- Review the GEMINI_API_INTEGRATION.md file for technical details
- Test with simple requests first
- Verify your API key permissions

### Useful Links
- [Google AI Studio](https://makersuite.google.com/)
- [Gemini API Documentation](https://ai.google.dev/docs)
- [OpenRouter Documentation](https://openrouter.ai/docs)
- [n8n Documentation](https://docs.n8n.io/)

### Community
- Share your experience with different models
- Report issues or suggestions
- Contribute to the extension development
- Help other users with setup questions
