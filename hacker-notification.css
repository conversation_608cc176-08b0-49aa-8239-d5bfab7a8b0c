/**
 * n8n boy Notification Styles - Variant 1: Minimal Matrix
 * Optimized for performance and visual consistency
 */

.n8n-boy-hacker-notification {
  background: rgba(0, 0, 0, 0.95) !important;
  color: #00ff41 !important;
  border: 1px solid #00ff41 !important;
  border-radius: 4px !important;
  padding: 12px 16px !important;
  font-family: 'IBM Plex Mono', ui-monospace, Consolas, 'Liberation Mono', Menlo, monospace !important;
  font-size: 13px !important;
  box-shadow: 0 0 15px rgba(0, 255, 65, 0.3) !important;
  display: flex !important;
  align-items: flex-start !important;
  gap: 10px !important;
  min-width: 280px !important;
  max-width: 400px !important;
  position: fixed !important;
  z-index: 999999 !important;
  pointer-events: none !important;
  user-select: none !important;
  backdrop-filter: blur(4px) !important;
}

.n8n-boy-hacker-notification .content {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.n8n-boy-hacker-notification .icon {
  width: 20px;
  height: 20px;
  background: #00ff41;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
}

.n8n-boy-hacker-notification .text {
  flex: 1;
  color: #00ff41;
}

.n8n-boy-hacker-notification .cursor {
  display: inline-block;
  width: 8px;
  height: 14px;
  background: #00ff41;
  margin-left: 2px;
  animation: cursorBlink 1s infinite;
}

.n8n-boy-hacker-notification .typing-text {
  color: #00ff41;
}

.n8n-boy-hacker-notification .warning-text {
  font-size: 11px;
  color: #ff9800;
  margin-top: 4px;
  opacity: 0.85;
  font-style: italic;
  letter-spacing: 0.3px;
}

/* Optimized animations for performance */
@keyframes slideInFade {
  from {
    opacity: 0;
    transform: translate(-50%, -60%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

@keyframes slideOutFade {
  from {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
  to {
    opacity: 0;
    transform: translate(-50%, -40%);
  }
}

@keyframes cursorBlink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}
