# n8n Boy Extension - Gemini API Integration

## Overview

The n8n Boy extension has been enhanced to support both the existing OpenRouter API and the new direct Google Gemini API integration. This provides users with more flexibility and potentially better performance when using Gemini models.

## Key Features

### Dual API Support
- **OpenRouter API**: Existing functionality maintained for backward compatibility
- **Gemini API**: Direct integration with Google's Gemini API for improved performance and reliability

### Enhanced Model Selection
The extension now supports the following models:

#### OpenRouter Models (Existing)
- Claude Sonnet 4 (🚀 Awesome)
- Kimi K2 (⚡ Super good)
- OpenAI GPT-4.1 (🔥 Pretty good)
- DeepSeek Chat V3 (🔥 Pretty good)
- Gemini 2.5 Pro via OpenRouter (🐌 Good but slow)
- Grok 4 (🐌 Good but slow)
- Mistral Large (⚡ Decent but fast)

#### Direct Gemini API Models (New)
- **Gemini 2.0 Flash** (⚡ Direct API) - Latest Gemini model via direct Google API
- **Gemini 1.5 Pro** (🚀 Direct API) - Powerful Gemini Pro model via direct Google API
- **Gemini 1.5 Flash** (⚡ Direct API) - Fast and efficient Gemini model via direct Google API

## Configuration

### API Keys Setup

#### For OpenRouter (Existing)
1. Click on the n8n Boy extension icon in your browser toolbar
2. Enter your OpenRouter API key (starts with `sk-or-v1-`)
3. Select an OpenRouter model from the dropdown

#### For Gemini API (New)
1. Get your Gemini API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Click on the n8n Boy extension icon in your browser toolbar
3. Enter your Gemini API key (starts with `AIza`)
4. Select a Gemini model from the dropdown

### Storage Keys
The extension stores API keys in Chrome's sync storage:
- **OpenRouter**: `openrouterApiKey`
- **Gemini**: `geminiApiKey`

## Technical Implementation

### Enhanced Configuration Structure
```javascript
const t = {
  // OpenRouter API configuration (existing)
  openrouter: {
    apiEndpoint: "https://openrouter.ai/api/v1/chat/completions",
    keyPrefix: "sk-or-v1-",
    keyMinLength: 30,
    defaultModel: "anthropic/claude-sonnet-4"
  },
  
  // Gemini API configuration (new)
  gemini: {
    apiEndpoint: "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent",
    keyPrefix: "AIza",
    keyMinLength: 35,
    defaultModel: "gemini-2.0-flash-exp"
  }
}
```

### API Provider Detection
The system automatically detects which API to use based on:
1. **Model ID**: Models with `gemini-` prefix or containing "gemini" use the Gemini API
2. **API Key Format**: Keys starting with `AIza` are treated as Gemini API keys
3. **Model Configuration**: Each model has an `apiProvider` property specifying the provider

### Message Format Conversion
The extension automatically converts between OpenAI-style messages and Gemini's format:

#### OpenAI Format (Input)
```javascript
[
  {role: "system", content: "You are a helpful assistant"},
  {role: "user", content: "Hello"},
  {role: "assistant", content: "Hi there!"}
]
```

#### Gemini Format (Converted)
```javascript
{
  contents: [
    {role: "user", parts: [{text: "You are a helpful assistant\nHello"}]},
    {role: "model", parts: [{text: "Hi there!"}]}
  ]
}
```

## Benefits of Direct Gemini API

### Performance
- **Faster Response Times**: Direct API calls eliminate the OpenRouter proxy layer
- **Better Reliability**: Reduced dependency on third-party services
- **Lower Latency**: Direct connection to Google's infrastructure

### Cost Efficiency
- **Direct Billing**: Pay Google directly without markup
- **Better Rate Limits**: Access to Google's native rate limits
- **Transparent Pricing**: Clear understanding of API costs

### Feature Access
- **Latest Models**: Access to newest Gemini models as soon as they're released
- **Full Feature Set**: Access to all Gemini API features and parameters
- **Better Error Handling**: More detailed error messages from the source

## Backward Compatibility

### Existing Functionality Preserved
- All existing OpenRouter integrations continue to work unchanged
- Existing API keys and configurations remain valid
- No breaking changes to the user interface

### Migration Path
Users can gradually migrate to Gemini API by:
1. Obtaining a Gemini API key
2. Selecting a direct Gemini model
3. Testing functionality with the new setup
4. Keeping OpenRouter as a fallback option

## Error Handling

### Enhanced Error Messages
The system provides specific error messages for each API provider:

#### OpenRouter Errors
- "Unknown OpenRouter API error"
- OpenRouter-specific error codes and messages

#### Gemini API Errors
- "Gemini API error: [status code]"
- Detailed error information from Google's API
- "Invalid response format from Gemini API"

### Fallback Mechanisms
- If a Gemini API call fails, the system provides clear error messages
- Users can switch between providers easily
- API key validation prevents invalid configurations

## Usage Examples

### Code Generation
```javascript
// The extension automatically routes to the appropriate API
// based on the selected model
const result = await generateCode({
  nodeType: "code",
  description: "Process user data and extract email addresses"
});
```

### Multi-Agent Chat
```javascript
// Works seamlessly with both OpenRouter and Gemini models
const response = await chatWithAgents(
  "How do I set up a webhook in n8n?",
  nodeContext,
  conversationHistory
);
```

## Troubleshooting

### Common Issues

#### API Key Not Working
1. Verify the API key format matches the provider
2. Check that the key has proper permissions
3. Ensure the key hasn't expired

#### Model Not Available
1. Confirm the model ID is correct
2. Check if the model requires special access
3. Verify your API key has access to the model

#### Connection Errors
1. Check your internet connection
2. Verify the API endpoint is accessible
3. Check for firewall or proxy issues

### Debug Information
The extension provides detailed debug information accessible through the browser console:
- API provider detection
- Request/response details
- Error stack traces
- Configuration status

## Future Enhancements

### Planned Features
- **Streaming Support**: Real-time response streaming for Gemini API
- **Advanced Configuration**: Fine-tuned parameters for each provider
- **Usage Analytics**: Track API usage and costs
- **Model Comparison**: Side-by-side comparison of different models

### Community Contributions
The enhanced architecture makes it easier to add support for additional AI providers in the future.
