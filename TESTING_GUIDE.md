# n8n Boy Extension - Testing Guide for Gemini API Integration

## Quick Verification Steps

### 1. Test Configuration Loading
1. Open the test file: `test_popup.html` in your browser
2. Verify all tests show "SUCCESS" status
3. Check that both OpenRouter and Gemini models are listed

### 2. Test Extension Popup
1. Load the extension in Chrome (Developer mode)
2. Click the extension icon to open the popup
3. Verify you can see the new Gemini models in the dropdown:
   - **Gemini 2.0 Flash (⚡ Direct API)**
   - **Gemini 1.5 Pro (🚀 Direct API)**
   - **Gemini 1.5 Flash (⚡ Direct API)**

### 3. Test API Key Switching
1. Select a Gemini model from the dropdown
2. Verify the API key field changes to "Gemini API Key"
3. Verify the placeholder shows "AIza..."
4. Select an OpenRouter model
5. Verify the API key field changes back to "OpenRouter API Key"
6. Verify the placeholder shows "sk-or-v1-..."

## Detailed Testing Procedures

### Testing Model Selection

#### Expected Gemini Models
When you open the model dropdown, you should see these new options:
```
✅ Gemini 2.0 Flash (⚡ Direct API)
✅ Gemini 1.5 Pro (🚀 Direct API)  
✅ Gemini 1.5 Flash (⚡ Direct API)
```

#### Expected OpenRouter Models (Existing)
These should still be available:
```
✅ Claude Sonnet 4 (🚀 Awesome)
✅ Kimi K2 (⚡ Super good)
✅ OpenAI GPT-4.1 (🔥 Pretty good)
✅ DeepSeek Chat V3 (🔥 Pretty good)
✅ Gemini 2.5 Pro (🐌 Good but slow) [via OpenRouter]
✅ Grok 4 (🐌 Good but slow)
✅ Mistral Large (⚡ Decent but fast)
```

### Testing API Key Configuration

#### For Gemini API Keys
1. **Get a Gemini API Key**:
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Sign in with your Google account
   - Click "Create API Key"
   - Copy the key (starts with `AIza`)

2. **Test Gemini API Key**:
   - Select a Gemini model (e.g., "Gemini 2.0 Flash")
   - Paste your Gemini API key
   - The extension should automatically detect it's a Gemini key
   - Save the configuration

3. **Verify Gemini Key Storage**:
   - Open Chrome DevTools (F12)
   - Go to Application > Storage > Extension Storage
   - Look for `geminiApiKey` entry

#### For OpenRouter API Keys
1. **Test OpenRouter API Key**:
   - Select an OpenRouter model (e.g., "Claude Sonnet 4")
   - Enter your OpenRouter API key (starts with `sk-or-v1-`)
   - Save the configuration

2. **Verify OpenRouter Key Storage**:
   - Check for `openrouterApiKey` entry in extension storage

### Testing API Key Validation

#### Valid API Key Formats
- **Gemini**: `AIzaSyDXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX` (35+ characters)
- **OpenRouter**: `sk-or-v1-XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX` (30+ characters)

#### Invalid API Key Tests
Try these invalid keys to ensure validation works:
- `invalid-key` → Should show error
- `sk-or-v1-short` → Should show error for OpenRouter
- `AIza-short` → Should show error for Gemini

### Testing Dual API Functionality

#### Scenario 1: Switch Between Providers
1. Configure both Gemini and OpenRouter API keys
2. Select a Gemini model → Should load Gemini API key
3. Select an OpenRouter model → Should load OpenRouter API key
4. Verify the correct API key appears for each model type

#### Scenario 2: Test API Calls
1. **Test with n8n Workflow**:
   - Open any n8n workflow
   - Add a Code node
   - Right-click and select "Generate with AI"
   - Try with both Gemini and OpenRouter models
   - Verify responses are generated correctly

2. **Test Multi-Agent Chat**:
   - Use the extension's chat feature
   - Test with different models
   - Verify responses come from the correct API

### Browser Console Testing

#### Enable Debug Mode
1. Open Chrome DevTools (F12)
2. Go to Console tab
3. Look for n8n Boy debug messages

#### Expected Console Messages
When switching models, you should see:
```
n8n Boy: Provider detected: gemini for model: gemini-2.0-flash-exp
n8n Boy: Loading API key for provider: gemini
n8n Boy: API key loaded successfully
```

### Error Testing

#### Test Error Handling
1. **Invalid API Key**: Enter wrong format → Should show validation error
2. **Network Error**: Disconnect internet → Should show connection error
3. **API Error**: Use expired key → Should show API error

#### Expected Error Messages
- **Gemini API Error**: "Gemini API error: [status code]"
- **OpenRouter API Error**: "Unknown OpenRouter API error"
- **Validation Error**: "Invalid API key format"

## Troubleshooting Common Issues

### Issue: Gemini Models Not Showing
**Solution**: 
1. Check if `llm-config-global.js` was updated correctly
2. Reload the extension
3. Clear extension storage and reconfigure

### Issue: API Key Not Saving
**Solution**:
1. Check browser console for errors
2. Verify Chrome storage permissions
3. Try clearing extension data and reconfiguring

### Issue: Wrong API Key Loaded
**Solution**:
1. Verify model has correct `apiProvider` property
2. Check provider detection logic
3. Clear cache and reload extension

### Issue: API Calls Failing
**Solution**:
1. Verify API key is valid and has permissions
2. Check network connectivity
3. Verify API endpoint URLs are correct

## Performance Testing

### Response Time Comparison
Test response times between providers:
1. **OpenRouter Models**: Measure time for code generation
2. **Gemini Direct API**: Measure time for same request
3. **Expected**: Gemini direct should be faster

### Load Testing
1. Make multiple rapid requests
2. Verify both APIs handle load correctly
3. Check for rate limiting behavior

## Success Criteria

### ✅ Configuration Tests
- [ ] Both API configurations load correctly
- [ ] All models appear in dropdown
- [ ] Provider detection works for all models

### ✅ UI Tests  
- [ ] API key field updates based on selected model
- [ ] Placeholder text changes correctly
- [ ] Validation messages are appropriate

### ✅ Functionality Tests
- [ ] Both API types can generate code
- [ ] API key storage works for both providers
- [ ] Error handling works for both APIs

### ✅ Integration Tests
- [ ] Extension works with n8n workflows
- [ ] Multi-agent chat functions correctly
- [ ] All existing features still work

## Reporting Issues

If you encounter any issues:

1. **Check Browser Console**: Look for error messages
2. **Verify Configuration**: Use the test HTML file
3. **Test API Keys**: Verify keys work outside the extension
4. **Document Steps**: Record exact steps to reproduce the issue

### Information to Include in Bug Reports
- Browser version and OS
- Extension version
- Selected model and provider
- API key format (first 10 characters only)
- Error messages from console
- Steps to reproduce

## Next Steps After Testing

Once testing is complete:
1. **Production Deployment**: Load the extension in production
2. **User Training**: Share setup guides with users
3. **Monitoring**: Monitor API usage and performance
4. **Feedback Collection**: Gather user feedback on new features
