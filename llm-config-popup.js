!function(){"use strict";const e={apiEndpoint:"https://openrouter.ai/api/v1/chat/completions",keyPrefix:"sk-or-v1-",keyMinLength:30,defaultModel:"anthropic/claude-sonnet-4",models:{"anthropic/claude-sonnet-4":{id:"anthropic/claude-sonnet-4",name:"Claude Sonnet 4 (🚀 Awesome)",provider:"Anthropic",description:"Most capable model for complex reasoning and analysis",contextLength:1e6,supportsImages:!0,supportsTools:!0},"moonshotai/kimi-k2":{id:"moonshotai/kimi-k2",name:"<PERSON><PERSON> K2 (⚡ Super good)",provider:"Moonshot AI",description:"High-performance Chinese-English bilingual model",contextLength:128e3,supportsImages:!1,supportsTools:!0},"openai/gpt-4.1":{id:"openai/gpt-4.1",name:"OpenAI GPT-4.1 (🔥 Pretty good)",provider:"OpenAI",description:"Latest GPT-4 model with enhanced capabilities",contextLength:128e3,supportsImages:!0,supportsTools:!0},"deepseek/deepseek-chat-v3-0324":{id:"deepseek/deepseek-chat-v3-0324",name:"DeepSeek Chat V3 (🔥 Pretty good)",provider:"DeepSeek",description:"Advanced conversational model with strong performance",contextLength:128e3,supportsImages:!1,supportsTools:!0},"google/gemini-2.5-pro-preview":{id:"google/gemini-2.5-pro-preview",name:"Gemini 2.5 Pro (🐌 Good but slow)",provider:"Google",description:"Advanced multimodal model with strong reasoning",contextLength:1e6,supportsImages:!0,supportsTools:!0},"x-ai/grok-4":{id:"x-ai/grok-4",name:"Grok 4 (🐌 Good but slow)",provider:"xAI",description:"Advanced reasoning model with real-time knowledge",contextLength:128e3,supportsImages:!0,supportsTools:!0},"mistralai/mistral-large-2407":{id:"mistralai/mistral-large-2407",name:"Mistral Large (⚡ Decent but fast)",provider:"Mistral",description:"High-performance model for complex reasoning tasks",contextLength:128e3,supportsImages:!1,supportsTools:!0}},temperatures:{codeGeneration:.2,promptOptimization:.3,promptGeneration:.7,jsonGeneration:.5,jsonFixing:.3,n8nHelper:.4,agentRouting:.1,workflowAgent:.4,troubleshootingAgent:.3,integrationAgent:.4,generalAgent:.5,default:.7},maxTokens:3e3,optionalHeaders:{httpReferer:void 0,xTitle:"n8n Boy Extension"}};function o(o){return e.models[o]||null}function t(o){return Object.values(e.models).find((e=>e.id===o))||null}function n(o){return e.temperatures[o]||e.temperatures.default}function i(o){return!!(o&&o.startsWith(e.keyPrefix)&&o.length>=e.keyMinLength)}function s(){return Object.keys(e.models)}function r(){return Object.values(e.models).map((e=>e.id))}function a(){const o=Object.keys(e.models).find((o=>e.models[o].id===e.defaultModel));return e.models[o||"anthropic/claude-sonnet-4"]}!function(){const p=window;p.LLM_CONFIG=e,p.getModelConfig=o,p.getModelConfigById=t,p.getTemperature=n,p.validateApiKey=i,p.getModelNames=s,p.getModelIds=r,p.getDefaultModel=a}()}();
